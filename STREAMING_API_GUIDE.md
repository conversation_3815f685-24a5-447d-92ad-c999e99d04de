# Streaming API Guide

This guide explains how to use the new streaming endpoints in The Infini AI Backend.

## Overview

The backend now supports real-time streaming responses using Server-Sent Events (SSE). This allows the frontend to receive AI responses as they are generated, providing a better user experience with real-time feedback.

## Important: Understanding IDs and Conversation Continuity

The backend uses different ID systems for different conversation types:

### Chat System (Regular & Guest Chats)
- **sessionId**: String identifier for conversation continuity
- **chatId**: Internal UUID (not exposed to frontend)
- **Use `sessionId` in requests** to continue existing conversations

### Thread System (Regular & Project Threads)
- **threadId**: UUID identifier for conversation continuity
- **sessionId**: Internal string identifier (also returned for compatibility)
- **Use `threadId` in requests** to continue existing threads

### Key Rule:
- **Chat endpoints**: Use `sessionId` for conversation continuity
- **Thread endpoints**: Use `threadId` for conversation continuity

## Workflow Examples

### Chat Conversation Flow
```
1. First message: POST /api/chat/message/stream
   Request: {"message": "Hello"}
   Response: {"sessionId": "abc123", ...}

2. Second message: POST /api/chat/message/stream
   Request: {"message": "How are you?", "sessionId": "abc123"}
   Response: {"sessionId": "abc123", ...} // Same conversation
```

### Thread Conversation Flow
```
1. First message: POST /api/threads/message/stream
   Request: {"message": "Hello"}
   Response: {"threadId": "uuid-123", "sessionId": "def456", ...}

2. Second message: POST /api/threads/message/stream
   Request: {"message": "How are you?", "threadId": "uuid-123"}
   Response: {"threadId": "uuid-123", "sessionId": "def456", ...} // Same thread
```

## Streaming Endpoints

### 1. Chat Messages (Regular & Guest)

**Endpoint:** `POST /api/chat/message/stream`

**Description:** Send a chat message and receive a streaming response

**Headers:**
```
Content-Type: application/json
Authorization: Bearer <token> (for authenticated users)
X-Session-ID: <session-id> (for guest users)
```

**Request Body:**
```json
{
  "message": "Your message here",
  "sessionId": "session-id-for-conversation-continuity",
  "llmModel": "gpt-3.5-turbo"
}
```

**Important:** To maintain conversation history, always include the `sessionId` from previous responses. If no `sessionId` is provided, a new conversation will be started.

**Response:** Server-Sent Events stream

### 2. Thread Messages

**Endpoint:** `POST /api/threads/message/stream`

**Description:** Send a message in a thread and receive a streaming response

**Headers:**
```
Content-Type: application/json
Authorization: Bearer <token> (required)
```

**Request Body:**
```json
{
  "message": "Your message here",
  "threadId": "thread-id-for-conversation-continuity",
  "llmModel": "gpt-3.5-turbo"
}
```

**Important:** To maintain conversation history, always include the `threadId` from previous responses. If no `threadId` is provided, a new thread will be created.

### 3. Project Messages

**Endpoint:** `POST /api/projects/:projectId/message/stream`

**Description:** Send a message in a project thread and receive a streaming response

**Headers:**
```
Content-Type: application/json
Authorization: Bearer <token> (required)
```

**Request Body:**
```json
{
  "message": "Your message here",
  "threadId": "thread-id-for-conversation-continuity",
  "llmModel": "gpt-3.5-turbo"
}
```

**Important:** To maintain conversation history within the project, always include the `threadId` from previous responses. If no `threadId` is provided, a new project thread will be created.

## Server-Sent Events Format

The streaming response uses Server-Sent Events (SSE) format. Each event contains JSON data:

### Event Types

#### 1. Start Event
```json
{
  "type": "start",
  "message": "Response started",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### 2. Chunk Event (Content)
```json
{
  "type": "chunk",
  "content": "Part of the AI response...",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### 3. Complete Event
```json
{
  "type": "complete",
  "fullResponse": "The complete AI response",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### 4. Error Event
```json
{
  "type": "error",
  "error": "Error message",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Frontend Implementation Example

### React Hook Example

```javascript
import { useState, useCallback } from 'react';

export function useStreamingChat() {
  const [isStreaming, setIsStreaming] = useState(false);
  const [response, setResponse] = useState('');

  const sendMessage = useCallback(async (message, options = {}) => {
    setIsStreaming(true);
    setResponse('');

    try {
      const response = await fetch('/api/chat/message/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        body: JSON.stringify({
          message,
          ...options.body
        })
      });

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop();

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.type === 'chunk') {
                setResponse(prev => prev + data.content);
              } else if (data.type === 'error') {
                throw new Error(data.error);
              }
            } catch (e) {
              console.error('Failed to parse SSE data:', e);
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
    } finally {
      setIsStreaming(false);
    }
  }, []);

  return { sendMessage, isStreaming, response };
}
```

## Error Handling

- Network errors: Handle fetch/connection failures
- Parse errors: Handle malformed SSE data
- Stream errors: Handle error events from the server
- Timeout handling: Implement client-side timeouts if needed

## Rate Limiting

Streaming endpoints use the same rate limiting as regular endpoints:
- 20 messages per minute per user
- Same authentication and authorization rules apply
