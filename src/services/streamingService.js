class StreamingService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5529/api';
  }

  /**
   * Create streaming request with proper headers
   */
  createStreamingRequest(endpoint, data, options = {}) {
    const token = localStorage.getItem('authToken');
    const csrfToken = localStorage.getItem('csrfToken');
    const sessionId = localStorage.getItem('sessionId');

    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add authentication headers
    if (token) {
      headers.Authorization = `Bearer ${token}`;
      if (csrfToken) {
        headers['X-CSRF-Token'] = csrfToken;
      }
    } else if (sessionId) {
      headers['X-Session-ID'] = sessionId;
    }

    const requestOptions = {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
      ...options
    };

    return fetch(`${this.baseURL}${endpoint}`, requestOptions);
  }

  /**
   * Parse Server-Sent Events stream
   */
  async parseSSEStream(response, callbacks = {}) {
    const {
      onStart = () => {},
      onChunk = () => {},
      onComplete = () => {},
      onError = () => {},
      onProgress = () => {}
    } = callbacks;

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              switch (data.type) {
                case 'start':
                  onStart(data);
                  break;
                  
                case 'chunk':
                  fullResponse += data.content;
                  onChunk(data.content, fullResponse);
                  onProgress(fullResponse);
                  break;
                  
                case 'complete':
                  onComplete(data.fullResponse || fullResponse, data);
                  return data.fullResponse || fullResponse;
                  
                case 'error':
                  onError(new Error(data.error));
                  throw new Error(data.error);
                  
                default:
                  console.warn('Unknown SSE event type:', data.type);
              }
            } catch (parseError) {
              console.error('Failed to parse SSE data:', parseError, 'Line:', line);
            }
          }
        }
      }
    } catch (error) {
      onError(error);
      throw error;
    } finally {
      reader.releaseLock();
    }

    return fullResponse;
  }

  /**
   * Stream chat message (regular or guest)
   */
  async streamChatMessage(message, options = {}) {
    const { sessionId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;
    
    const data = {
      message,
      llmModel
    };

    if (sessionId) {
      data.sessionId = sessionId;
    }

    const response = await this.createStreamingRequest('/chat/message/stream', data);
    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream thread message
   */
  async streamThreadMessage(message, options = {}) {
    const { threadId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;
    
    const data = {
      message,
      llmModel
    };

    if (threadId) {
      data.threadId = threadId;
    }

    const response = await this.createStreamingRequest('/threads/message/stream', data);
    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream project message
   */
  async streamProjectMessage(projectId, message, options = {}) {
    const { threadId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;
    
    const data = {
      message,
      llmModel
    };

    if (threadId) {
      data.threadId = threadId;
    }

    const response = await this.createStreamingRequest(`/projects/${projectId}/message/stream`, data);
    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Handle streaming errors
   */
  handleStreamingError(error) {
    if (error.name === 'AbortError') {
      return new Error('Request was cancelled');
    }
    
    if (error.message.includes('Failed to fetch')) {
      return new Error('Network error: Please check your connection');
    }
    
    if (error.message.includes('HTTP 401')) {
      return new Error('Authentication required');
    }
    
    if (error.message.includes('HTTP 403')) {
      return new Error('Access denied');
    }
    
    if (error.message.includes('HTTP 429')) {
      return new Error('Rate limit exceeded. Please wait before sending another message.');
    }
    
    return error;
  }
}

const streamingService = new StreamingService();
export default streamingService;
